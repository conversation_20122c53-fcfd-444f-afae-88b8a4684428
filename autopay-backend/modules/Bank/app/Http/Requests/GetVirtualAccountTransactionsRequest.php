<?php

namespace Modules\Bank\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetVirtualAccountTransactionsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'order' => 'sometimes|integer|min:1',
            'page' => 'sometimes|integer|min:0',
            'size' => 'sometimes|integer|min:1|max:500',
            'bank_account_no' => 'sometimes|string',
            'from_date' => 'sometimes|date_format:Y-m-d H:i:s',
            'to_date' => 'sometimes|date_format:Y-m-d H:i:s|after_or_equal:from_date',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'order.integer' => 'Thứ tự phải là số nguyên.',
            'order.min' => 'Thứ tự phải ít nhất là 1.',
            'page.integer' => 'Trang phải là số nguyên.',
            'page.min' => 'Trang phải ít nhất là 0.',
            'size.integer' => 'Kích thước phải là số nguyên.',
            'size.min' => 'Kích thước phải ít nhất là 1.',
            'size.max' => 'Kích thước không được vượt quá 500.',
            'bank_account_no.string' => 'Số tài khoản ngân hàng phải là chuỗi ký tự.',
            'from_date.date_format' => 'Ngày bắt đầu phải có định dạng Y-m-d H:i:s.',
            'to_date.date_format' => 'Ngày kết thúc phải có định dạng Y-m-d H:i:s.',
            'to_date.after_or_equal' => 'Ngày kết thúc phải sau hoặc bằng ngày bắt đầu.',
        ];
    }
}
