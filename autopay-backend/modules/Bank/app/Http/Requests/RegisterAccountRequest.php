<?php

namespace Modules\Bank\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RegisterAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accountNumber' => 'required|string|min:6|max:20',
            'accountName' => 'sometimes|string|max:255',
            'idCardNumber' => 'sometimes|string|min:9|max:12',
            'phoneNumber' => 'sometimes|string|min:10|max:11',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'accountNumber.required' => 'Số tài khoản là bắt buộc.',
            'accountNumber.string' => 'Số tài khoản phải là chuỗi ký tự.',
            'accountNumber.min' => 'Số tài khoản phải có ít nhất 6 ký tự.',
            'accountNumber.max' => 'Số tài khoản không được vượt quá 20 ký tự.',
            'accountName.string' => 'Tên tài khoản phải là chuỗi ký tự.',
            'accountName.max' => 'Tên tài khoản không được vượt quá 255 ký tự.',
            'idCardNumber.string' => 'Số CMND/CCCD phải là chuỗi ký tự.',
            'idCardNumber.min' => 'Số CMND/CCCD phải có ít nhất 9 ký tự.',
            'idCardNumber.max' => 'Số CMND/CCCD không được vượt quá 12 ký tự.',
            'phoneNumber.string' => 'Số điện thoại phải là chuỗi ký tự.',
            'phoneNumber.min' => 'Số điện thoại phải có ít nhất 10 ký tự.',
            'phoneNumber.max' => 'Số điện thoại không được vượt quá 11 ký tự.',
        ];
    }
}
