<?php

namespace Modules\Bank\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VerifyLinkedAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'session_id' => 'required|string',
            'otp' => 'required|string|min:4|max:10',
            'accountNumber' => 'required|string|min:6|max:20',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'session_id.required' => 'Session ID là bắt buộc.',
            'session_id.string' => 'Session ID phải là chuỗi ký tự.',
            'otp.required' => 'Mã OTP là bắt buộc.',
            'otp.string' => 'Mã OTP phải là chuỗi ký tự.',
            'otp.min' => 'Mã OTP phải có ít nhất 4 ký tự.',
            'otp.max' => 'Mã OTP không được vượt quá 10 ký tự.',
            'accountNumber.required' => 'Số tài khoản là bắt buộc.',
            'accountNumber.string' => 'Số tài khoản phải là chuỗi ký tự.',
            'accountNumber.min' => 'Số tài khoản phải có ít nhất 6 ký tự.',
            'accountNumber.max' => 'Số tài khoản không được vượt quá 20 ký tự.',
        ];
    }
}
