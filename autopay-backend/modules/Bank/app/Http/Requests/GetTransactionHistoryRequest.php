<?php

namespace Modules\Bank\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetTransactionHistoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accountNumber' => 'required|string|min:6|max:20',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'limit' => 'sometimes|integer|min:1|max:100',
            'page' => 'sometimes|integer|min:1',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'accountNumber.required' => 'Số tài khoản là bắt buộc.',
            'accountNumber.string' => 'Số tài khoản phải là chuỗi ký tự.',
            'accountNumber.min' => 'Số tài khoản phải có ít nhất 6 ký tự.',
            'accountNumber.max' => 'Số tài khoản không được vượt quá 20 ký tự.',
            'start_date.date' => 'Ngày bắt đầu phải là định dạng ngày hợp lệ.',
            'end_date.date' => 'Ngày kết thúc phải là định dạng ngày hợp lệ.',
            'end_date.after_or_equal' => 'Ngày kết thúc phải sau hoặc bằng ngày bắt đầu.',
            'limit.integer' => 'Giới hạn phải là số nguyên.',
            'limit.min' => 'Giới hạn phải ít nhất là 1.',
            'limit.max' => 'Giới hạn không được vượt quá 100.',
            'page.integer' => 'Trang phải là số nguyên.',
            'page.min' => 'Trang phải ít nhất là 1.',
        ];
    }
}
