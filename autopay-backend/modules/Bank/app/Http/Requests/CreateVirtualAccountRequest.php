<?php

namespace Modules\Bank\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateVirtualAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'accountNumber' => 'required|string|min:6|max:20',
            'virtual_accountName' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:500',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'accountNumber.required' => 'Số tài khoản là bắt buộc.',
            'accountNumber.string' => 'Số tài khoản phải là chuỗi ký tự.',
            'accountNumber.min' => 'Số tài khoản phải có ít nhất 6 ký tự.',
            'accountNumber.max' => 'Số tài khoản không được vượt quá 20 ký tự.',
            'virtual_accountName.string' => 'Tên tài khoản ảo phải là chuỗi ký tự.',
            'virtual_accountName.max' => 'Tên tài khoản ảo không được vượt quá 255 ký tự.',
            'description.string' => 'Mô tả phải là chuỗi ký tự.',
            'description.max' => 'Mô tả không được vượt quá 500 ký tự.',
        ];
    }
}
